// 科技背景动画系统
class TechBackground {
    constructor() {
        this.particlesContainer = document.getElementById('particles');
        this.neuralNetwork = document.getElementById('neuralNetwork');
        this.particles = [];
        this.neuralNodes = [];
        this.connections = [];

        this.init();
    }

    init() {
        this.createParticles();
        this.createNeuralNetwork();
        this.startAnimations();
    }

    // 创建粒子系统
    createParticles() {
        const particleCount = 50;

        for (let i = 0; i < particleCount; i++) {
            setTimeout(() => {
                this.createParticle();
            }, i * 100);
        }

        // 持续创建新粒子
        setInterval(() => {
            this.createParticle();
        }, 200);
    }

    createParticle() {
        const particle = document.createElement('div');
        particle.className = 'particle';

        // 随机位置和属性
        const startX = Math.random() * window.innerWidth;
        const size = Math.random() * 3 + 1;
        const duration = Math.random() * 5 + 8;
        const delay = Math.random() * 2;

        particle.style.left = startX + 'px';
        particle.style.width = size + 'px';
        particle.style.height = size + 'px';
        particle.style.animationDuration = duration + 's';
        particle.style.animationDelay = delay + 's';

        // 随机颜色变化
        const colors = [
            'rgba(37, 99, 235, 0.8)',
            'rgba(59, 130, 246, 0.7)',
            'rgba(96, 165, 250, 0.6)',
            'rgba(147, 197, 253, 0.5)'
        ];
        particle.style.background = colors[Math.floor(Math.random() * colors.length)];

        this.particlesContainer.appendChild(particle);

        // 动画结束后移除粒子
        setTimeout(() => {
            if (particle.parentNode) {
                particle.parentNode.removeChild(particle);
            }
        }, (duration + delay) * 1000);
    }

    // 创建神经网络连线
    createNeuralNetwork() {
        const nodeCount = 8;
        const width = window.innerWidth;
        const height = window.innerHeight;

        // 创建节点
        for (let i = 0; i < nodeCount; i++) {
            const node = {
                x: Math.random() * width,
                y: Math.random() * height,
                vx: (Math.random() - 0.5) * 0.5,
                vy: (Math.random() - 0.5) * 0.5
            };
            this.neuralNodes.push(node);
        }

        this.updateNeuralNetwork();
    }

    updateNeuralNetwork() {
        // 清除现有连线
        this.neuralNetwork.innerHTML = this.neuralNetwork.querySelector('defs').outerHTML;

        // 更新节点位置
        this.neuralNodes.forEach(node => {
            node.x += node.vx;
            node.y += node.vy;

            // 边界反弹
            if (node.x <= 0 || node.x >= window.innerWidth) node.vx *= -1;
            if (node.y <= 0 || node.y >= window.innerHeight) node.vy *= -1;

            // 保持在边界内
            node.x = Math.max(0, Math.min(window.innerWidth, node.x));
            node.y = Math.max(0, Math.min(window.innerHeight, node.y));
        });

        // 创建连线
        for (let i = 0; i < this.neuralNodes.length; i++) {
            for (let j = i + 1; j < this.neuralNodes.length; j++) {
                const node1 = this.neuralNodes[i];
                const node2 = this.neuralNodes[j];
                const distance = Math.sqrt(
                    Math.pow(node1.x - node2.x, 2) + Math.pow(node1.y - node2.y, 2)
                );

                // 只连接距离较近的节点
                if (distance < 300) {
                    const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
                    line.setAttribute('x1', node1.x);
                    line.setAttribute('y1', node1.y);
                    line.setAttribute('x2', node2.x);
                    line.setAttribute('y2', node2.y);
                    line.setAttribute('class', 'neural-line');
                    line.style.opacity = (300 - distance) / 300 * 0.6;

                    this.neuralNetwork.appendChild(line);
                }
            }
        }

        // 继续更新
        requestAnimationFrame(() => this.updateNeuralNetwork());
    }

    startAnimations() {
        // 添加鼠标交互效果
        document.addEventListener('mousemove', (e) => {
            this.addMouseInteraction(e.clientX, e.clientY);
        });
    }

    addMouseInteraction(mouseX, mouseY) {
        // 在鼠标附近创建临时粒子
        if (Math.random() < 0.1) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = (mouseX + (Math.random() - 0.5) * 100) + 'px';
            particle.style.top = (mouseY + (Math.random() - 0.5) * 100) + 'px';
            particle.style.width = '3px';
            particle.style.height = '3px';
            particle.style.background = 'rgba(37, 99, 235, 0.9)';
            particle.style.animation = 'particlePulse 1s ease-out forwards';

            this.particlesContainer.appendChild(particle);

            setTimeout(() => {
                if (particle.parentNode) {
                    particle.parentNode.removeChild(particle);
                }
            }, 1000);
        }
    }
}

// 登录表单处理
class LoginForm {
    constructor() {
        this.form = document.getElementById('loginForm');
        this.button = this.form.querySelector('.login-button');
        this.init();
    }

    init() {
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });
    }

    handleLogin() {
        this.button.classList.add('loading');

        // 模拟登录过程
        setTimeout(() => {
            this.button.classList.remove('loading');
            alert('登录功能演示 - 实际项目中请连接后端API');
        }, 2000);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    new TechBackground();
    new LoginForm();
});