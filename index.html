<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业门户登录</title>
    <link rel="stylesheet" href="assets/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="tech-background">
        <!-- 粒子系统 -->
        <div class="particles-container" id="particles"></div>

        <!-- 神经网络连线 -->
        <svg class="neural-network" id="neuralNetwork">
            <defs>
                <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" style="stop-color:rgba(37, 99, 235, 0);stop-opacity:0" />
                    <stop offset="50%" style="stop-color:rgba(37, 99, 235, 0.6);stop-opacity:1" />
                    <stop offset="100%" style="stop-color:rgba(37, 99, 235, 0);stop-opacity:0" />
                </linearGradient>
                <filter id="glow">
                    <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                    <feMerge>
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                    </feMerge>
                </filter>
            </defs>
        </svg>

        <!-- 雷达扫描效果 -->
        <div class="radar-scanner">
            <div class="radar-sweep"></div>
            <div class="radar-rings">
                <div class="radar-ring ring-1"></div>
                <div class="radar-ring ring-2"></div>
                <div class="radar-ring ring-3"></div>
            </div>
        </div>

        <!-- 几何图形 -->
        <div class="geometric-shapes">
            <div class="geo-shape gear-1"></div>
            <div class="geo-shape gear-2"></div>
            <div class="geo-shape gear-3"></div>
            <div class="geo-shape hexagon-1"></div>
            <div class="geo-shape hexagon-2"></div>
        </div>

        <!-- 光晕效果 -->
        <div class="light-orbs">
            <div class="light-orb orb-1"></div>
            <div class="light-orb orb-2"></div>
            <div class="light-orb orb-3"></div>
        </div>
    </div>

    <div class="container">
        <div class="login-box">
            <div class="login-header">
                <div class="logo">
                    <div class="logo-icon">E</div>
                    <h1>Enterprise Portal</h1>
                </div>
                <h2>企业数字化管理平台</h2>
                <p class="welcome-text">欢迎使用企业门户系统，请登录您的账号</p>
            </div>

            <div class="login-form">
                <form id="loginForm">
                    <div class="form-group">
                        <div class="input-group">
                            <input type="text" id="username" required>
                            <label for="username">用户名</label>
                            <div class="input-line"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="input-group">
                            <input type="password" id="password" required>
                            <label for="password">密码</label>
                            <div class="input-line"></div>
                        </div>
                    </div>

                    <div class="form-options">
                        <label class="remember-me">
                            <input type="checkbox" id="remember">
                            <span class="checkmark"></span>
                            <span class="label-text">记住我</span>
                        </label>
                        <a href="#" class="forgot-link">忘记密码？</a>
                    </div>

                    <button type="submit" class="login-button">
                        <span class="button-text">登录</span>
                        <div class="button-loader"></div>
                    </button>
                </form>

                <div class="login-footer">
                    <p>还没有账号？ <a href="#" class="register-link">立即注册</a></p>
                </div>
            </div>
        </div>
    </div>
    <script src="assets/script.js"></script>
</body>
</html>