:root {
    --primary: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary: #3b82f6;
    --accent: #60a5fa;
    --background: #f8fafc;
    --surface: #ffffff;
    --text: #1e293b;
    --text-light: #64748b;
    --error: #ef4444;
    --success: #22c55e;
    --border: #e2e8f0;
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', sans-serif;
    background: #0f172a;
    color: var(--text);
    line-height: 1.5;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.tech-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
    overflow: hidden;
}

/* 粒子系统 */
.particles-container {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.particle {
    position: absolute;
    width: 2px;
    height: 2px;
    background: rgba(96, 165, 250, 0.8);
    border-radius: 50%;
    animation: particleFloat 8s infinite linear;
    box-shadow: 0 0 6px rgba(96, 165, 250, 0.6);
}

.particle::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: inherit;
    border-radius: 50%;
    animation: particlePulse 2s infinite ease-in-out;
}

/* 神经网络连线 */
.neural-network {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.neural-line {
    stroke: url(#lineGradient);
    stroke-width: 1;
    fill: none;
    filter: url(#glow);
    animation: lineFlow 3s infinite ease-in-out;
}

/* 雷达扫描效果 */
.radar-scanner {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 600px;
    height: 600px;
    transform: translate(-50%, -50%);
    opacity: 0.3;
}

.radar-sweep {
    position: absolute;
    width: 100%;
    height: 100%;
    background: conic-gradient(
        from 0deg,
        transparent 0deg,
        rgba(96, 165, 250, 0.4) 30deg,
        rgba(96, 165, 250, 0.1) 60deg,
        transparent 90deg
    );
    border-radius: 50%;
    animation: radarSweep 4s linear infinite;
}

.radar-rings {
    position: absolute;
    width: 100%;
    height: 100%;
}

.radar-ring {
    position: absolute;
    border: 1px solid rgba(96, 165, 250, 0.2);
    border-radius: 50%;
    animation: radarPulse 3s infinite ease-out;
}

.ring-1 {
    width: 200px;
    height: 200px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 0s;
}

.ring-2 {
    width: 400px;
    height: 400px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 1s;
}

.ring-3 {
    width: 600px;
    height: 600px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation-delay: 2s;
}

/* 几何图形 */
.geometric-shapes {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.geo-shape {
    position: absolute;
    opacity: 0.6;
}

.triangle-1 {
    width: 0;
    height: 0;
    border-left: 30px solid transparent;
    border-right: 30px solid transparent;
    border-bottom: 52px solid rgba(96, 165, 250, 0.3);
    top: 20%;
    right: 15%;
    animation: triangleFloat 12s infinite ease-in-out;
    filter: drop-shadow(0 0 10px rgba(96, 165, 250, 0.4));
}

.triangle-2 {
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-bottom: 35px solid rgba(147, 197, 253, 0.4);
    bottom: 25%;
    left: 10%;
    animation: triangleFloat 15s infinite ease-in-out reverse;
    filter: drop-shadow(0 0 8px rgba(147, 197, 253, 0.3));
}

.hexagon-1 {
    width: 40px;
    height: 23px;
    background: rgba(96, 165, 250, 0.2);
    position: relative;
    top: 60%;
    right: 25%;
    animation: hexagonSpin 20s infinite linear;
    filter: drop-shadow(0 0 12px rgba(96, 165, 250, 0.3));
}

.hexagon-1::before,
.hexagon-1::after {
    content: '';
    position: absolute;
    width: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
}

.hexagon-1::before {
    bottom: 100%;
    border-bottom: 12px solid rgba(96, 165, 250, 0.2);
}

.hexagon-1::after {
    top: 100%;
    border-top: 12px solid rgba(96, 165, 250, 0.2);
}

.hexagon-2 {
    width: 30px;
    height: 17px;
    background: rgba(147, 197, 253, 0.3);
    position: relative;
    top: 35%;
    left: 70%;
    animation: hexagonSpin 25s infinite linear reverse;
    filter: drop-shadow(0 0 10px rgba(147, 197, 253, 0.4));
}

.hexagon-2::before,
.hexagon-2::after {
    content: '';
    position: absolute;
    width: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
}

.hexagon-2::before {
    bottom: 100%;
    border-bottom: 9px solid rgba(147, 197, 253, 0.3);
}

.hexagon-2::after {
    top: 100%;
    border-top: 9px solid rgba(147, 197, 253, 0.3);
}

/* 光晕效果 */
.light-orbs {
    position: absolute;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.light-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    animation: orbFloat 15s infinite ease-in-out;
}

.orb-1 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(96, 165, 250, 0.4) 0%, transparent 70%);
    top: 10%;
    left: 80%;
    animation-delay: 0s;
}

.orb-2 {
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(147, 197, 253, 0.3) 0%, transparent 70%);
    bottom: 20%;
    left: 5%;
    animation-delay: -5s;
}

.orb-3 {
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, rgba(191, 219, 254, 0.5) 0%, transparent 70%);
    top: 70%;
    right: 10%;
    animation-delay: -10s;
}

/* 动画关键帧 */
@keyframes particleFloat {
    0% {
        transform: translateY(100vh) translateX(0) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 1;
        transform: translateY(90vh) translateX(10px) scale(1);
    }
    90% {
        opacity: 1;
        transform: translateY(10vh) translateX(-10px) scale(1);
    }
    100% {
        transform: translateY(-10vh) translateX(0) scale(0);
        opacity: 0;
    }
}

@keyframes particlePulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.5);
        opacity: 0.4;
    }
}

@keyframes lineFlow {
    0% {
        stroke-dasharray: 0 100;
        stroke-dashoffset: 0;
    }
    50% {
        stroke-dasharray: 50 50;
        stroke-dashoffset: -25;
    }
    100% {
        stroke-dasharray: 0 100;
        stroke-dashoffset: -100;
    }
}

@keyframes radarSweep {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes radarPulse {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 0;
    }
}

@keyframes triangleFloat {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.8;
    }
}

@keyframes hexagonSpin {
    0% {
        transform: rotate(0deg) scale(1);
    }
    50% {
        transform: rotate(180deg) scale(1.1);
    }
    100% {
        transform: rotate(360deg) scale(1);
    }
}

@keyframes orbFloat {
    0%, 100% {
        transform: translate(0, 0) scale(1);
        opacity: 0.4;
    }
    33% {
        transform: translate(30px, -20px) scale(1.1);
        opacity: 0.6;
    }
    66% {
        transform: translate(-20px, 30px) scale(0.9);
        opacity: 0.5;
    }
}

.container {
    width: 100%;
    max-width: 1200px;
    padding: 2rem;
    position: relative;
    z-index: 1;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.logo-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: white;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 700;
    margin-right: 1rem;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.logo h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text);
    letter-spacing: -0.5px;
}

.login-box {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 2rem;
    padding: 3rem;
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.5);
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.login-header {
    text-align: center;
    margin-bottom: 3rem;
}

.login-header h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text);
    margin-bottom: 1rem;
}

.welcome-text {
    color: var(--text-light);
    font-size: 1rem;
}

.form-group {
    margin-bottom: 2rem;
}

.input-group {
    position: relative;
}

.input-group input {
    width: 100%;
    padding: 1.25rem 0;
    font-size: 1.125rem;
    border: none;
    background: transparent;
    color: var(--text);
    outline: none;
}

.input-group label {
    position: absolute;
    left: 0;
    top: 1.25rem;
    color: var(--text-light);
    transition: all 0.3s ease;
    pointer-events: none;
    font-size: 1.125rem;
}

.input-group input:focus + label,
.input-group input:not(:placeholder-shown) + label {
    top: -0.5rem;
    font-size: 0.875rem;
    color: var(--primary);
}

.input-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--border);
    transition: all 0.3s ease;
}

.input-group input:focus ~ .input-line {
    background: var(--primary);
    height: 2px;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2.5rem;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.remember-me input {
    display: none;
}

.checkmark {
    width: 22px;
    height: 22px;
    border: 2px solid var(--border);
    border-radius: 6px;
    margin-right: 0.75rem;
    position: relative;
    transition: all 0.3s ease;
}

.remember-me input:checked + .checkmark {
    background: var(--primary);
    border-color: var(--primary);
}

.remember-me input:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 7px;
    top: 3px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.label-text {
    font-size: 0.875rem;
    color: var(--text-light);
}

.forgot-link {
    color: var(--primary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.forgot-link:hover {
    color: var(--primary-dark);
}

.login-button {
    width: 100%;
    padding: 1.25rem;
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: white;
    border: none;
    border-radius: 1rem;
    font-size: 1.125rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
}

.button-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    display: none;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

.login-button.loading .button-text {
    visibility: hidden;
}

.login-button.loading .button-loader {
    display: block;
}

.login-footer {
    text-align: center;
    margin-top: 2rem;
    font-size: 0.875rem;
    color: var(--text-light);
}

.register-link {
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.register-link:hover {
    color: var(--primary-dark);
}



@media (max-width: 1024px) {
    .radar-scanner {
        width: 400px;
        height: 400px;
    }

    .geometric-shapes .geo-shape {
        transform: scale(0.8);
    }
}

@media (max-width: 480px) {
    .container {
        padding: 1rem;
    }

    .login-box {
        padding: 2rem;
    }

    .logo h1 {
        font-size: 1.5rem;
    }

    .logo-icon {
        width: 48px;
        height: 48px;
        font-size: 1.5rem;
    }

    .login-header h2 {
        font-size: 1.5rem;
    }

    .input-group input {
        padding: 1rem 0;
        font-size: 1rem;
    }

    .input-group label {
        top: 1rem;
        font-size: 1rem;
    }

    .login-button {
        padding: 1rem;
        font-size: 1rem;
    }

    .radar-scanner {
        width: 300px;
        height: 300px;
    }

    .geometric-shapes .geo-shape {
        transform: scale(0.6);
    }

    .light-orbs .light-orb {
        transform: scale(0.7);
    }

    .particles-container .particle {
        width: 1px;
        height: 1px;
    }
}