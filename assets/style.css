:root {
    --primary: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary: #3b82f6;
    --accent: #60a5fa;
    --background: #f8fafc;
    --surface: #ffffff;
    --text: #1e293b;
    --text-light: #64748b;
    --error: #ef4444;
    --success: #22c55e;
    --border: #e2e8f0;
    --shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Noto Sans SC', sans-serif;
    background: #f0f9ff;
    color: var(--text);
    line-height: 1.5;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.tech-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    overflow: hidden;
}

.tech-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(96, 165, 250, 0.1);
    border: 2px solid rgba(96, 165, 250, 0.2);
    animation: float 20s infinite ease-in-out;
    box-shadow: 0 0 30px rgba(96, 165, 250, 0.1);
}

.tech-circle::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    background: conic-gradient(
        from 0deg,
        transparent 0deg,
        rgba(96, 165, 250, 0.2) 90deg,
        transparent 180deg,
        transparent 360deg
    );
    transform: translate(-50%, -50%);
    animation: rotate 15s linear infinite;
}

.tech-circle::after {
    content: '';
    position: absolute;
    inset: -2px;
    border-radius: 50%;
    background: conic-gradient(
        from 0deg,
        transparent 0deg,
        rgba(96, 165, 250, 0.3) 90deg,
        transparent 180deg,
        transparent 360deg
    );
    animation: rotate 12s linear infinite reverse;
}

.circle-1 {
    width: 1000px;
    height: 1000px;
    top: -400px;
    right: -300px;
    animation-delay: 0s;
}

.circle-2 {
    width: 800px;
    height: 800px;
    bottom: -300px;
    left: -300px;
    animation-delay: -5s;
}

.circle-3 {
    width: 600px;
    height: 600px;
    top: 40%;
    left: 20%;
    animation-delay: -10s;
}

.circle-4 {
    width: 400px;
    height: 400px;
    bottom: 30%;
    right: 20%;
    animation-delay: -15s;
}

.tech-line {
    position: absolute;
    background: linear-gradient(90deg, 
        transparent,
        rgba(96, 165, 250, 0.1),
        rgba(96, 165, 250, 0.3),
        rgba(96, 165, 250, 0.1),
        transparent
    );
    height: 2px;
    animation: scan 6s infinite linear;
    box-shadow: 0 0 20px rgba(96, 165, 250, 0.2);
}

.tech-line::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
        transparent,
        rgba(96, 165, 250, 0.5),
        transparent
    );
    transform: translateY(-50%);
    animation: pulse 2s infinite;
}

.line-1 {
    width: 100%;
    top: 20%;
    animation-delay: 0s;
}

.line-2 {
    width: 100%;
    top: 50%;
    animation-delay: -2s;
}

.line-3 {
    width: 100%;
    top: 80%;
    animation-delay: -4s;
}

.tech-dots {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(rgba(96, 165, 250, 0.2) 1px, transparent 1px),
        radial-gradient(rgba(96, 165, 250, 0.1) 1px, transparent 1px);
    background-size: 40px 40px, 20px 20px;
    background-position: 0 0, 20px 20px;
    opacity: 0.6;
    animation: dotsMove 20s linear infinite;
}

@keyframes float {
    0%, 100% {
        transform: translate(0, 0) rotate(0deg) scale(1);
    }
    25% {
        transform: translate(40px, 40px) rotate(5deg) scale(1.05);
    }
    50% {
        transform: translate(0, 60px) rotate(0deg) scale(1);
    }
    75% {
        transform: translate(-40px, 40px) rotate(-5deg) scale(0.95);
    }
}

@keyframes rotate {
    from {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

@keyframes scan {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 0.5;
        transform: translateY(-50%) scale(1);
    }
    50% {
        opacity: 1;
        transform: translateY(-50%) scale(1.2);
    }
}

@keyframes dotsMove {
    0% {
        background-position: 0 0, 20px 20px;
    }
    100% {
        background-position: 40px 40px, 60px 60px;
    }
}

.container {
    width: 100%;
    max-width: 1200px;
    padding: 2rem;
    position: relative;
    z-index: 1;
}

.login-wrapper {
    display: flex;
    background: var(--surface);
    border-radius: 2rem;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    min-height: 600px;
}

.login-left {
    flex: 1;
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    padding: 4rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.brand-content {
    position: relative;
    z-index: 2;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.logo-icon {
    width: 64px;
    height: 64px;
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: white;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 700;
    margin-right: 1rem;
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.2);
}

.logo h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text);
    letter-spacing: -0.5px;
}

.brand-description {
    margin-bottom: 3rem;
}

.brand-description h2 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.brand-description p {
    font-size: 1.125rem;
    opacity: 0.9;
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    backdrop-filter: blur(10px);
    transition: transform 0.3s ease;
}

.feature-item:hover {
    transform: translateX(10px);
}

.feature-icon {
    font-size: 1.5rem;
}

.feature-text h3 {
    font-size: 1.125rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.feature-text p {
    font-size: 0.875rem;
    opacity: 0.8;
}

.decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
}

.login-right {
    flex: 1;
    padding: 4rem;
    display: flex;
    align-items: center;
}

.login-box {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 2rem;
    padding: 3rem;
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.5);
    position: relative;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.login-header {
    text-align: center;
    margin-bottom: 3rem;
}

.login-header h2 {
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--text);
    margin-bottom: 1rem;
}

.welcome-text {
    color: var(--text-light);
    font-size: 1rem;
}

.form-group {
    margin-bottom: 2rem;
}

.input-group {
    position: relative;
}

.input-group input {
    width: 100%;
    padding: 1.25rem 0;
    font-size: 1.125rem;
    border: none;
    background: transparent;
    color: var(--text);
    outline: none;
}

.input-group label {
    position: absolute;
    left: 0;
    top: 1.25rem;
    color: var(--text-light);
    transition: all 0.3s ease;
    pointer-events: none;
    font-size: 1.125rem;
}

.input-group input:focus + label,
.input-group input:not(:placeholder-shown) + label {
    top: -0.5rem;
    font-size: 0.875rem;
    color: var(--primary);
}

.input-line {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--border);
    transition: all 0.3s ease;
}

.input-group input:focus ~ .input-line {
    background: var(--primary);
    height: 2px;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2.5rem;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.remember-me input {
    display: none;
}

.checkmark {
    width: 22px;
    height: 22px;
    border: 2px solid var(--border);
    border-radius: 6px;
    margin-right: 0.75rem;
    position: relative;
    transition: all 0.3s ease;
}

.remember-me input:checked + .checkmark {
    background: var(--primary);
    border-color: var(--primary);
}

.remember-me input:checked + .checkmark::after {
    content: '';
    position: absolute;
    left: 7px;
    top: 3px;
    width: 4px;
    height: 8px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

.label-text {
    font-size: 0.875rem;
    color: var(--text-light);
}

.forgot-link {
    color: var(--primary);
    text-decoration: none;
    font-size: 0.875rem;
    transition: color 0.3s ease;
}

.forgot-link:hover {
    color: var(--primary-dark);
}

.login-button {
    width: 100%;
    padding: 1.25rem;
    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
    color: white;
    border: none;
    border-radius: 1rem;
    font-size: 1.125rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.login-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(37, 99, 235, 0.3);
}

.button-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 24px;
    height: 24px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    display: none;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

.login-button.loading .button-text {
    visibility: hidden;
}

.login-button.loading .button-loader {
    display: block;
}

.login-footer {
    text-align: center;
    margin-top: 2rem;
    font-size: 0.875rem;
    color: var(--text-light);
}

.register-link {
    color: var(--primary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.register-link:hover {
    color: var(--primary-dark);
}

.background-shapes {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 0;
    pointer-events: none;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: var(--accent);
    opacity: 0.6;
    filter: blur(80px);
}

.shape-1 {
    width: 500px;
    height: 500px;
    top: -250px;
    right: -150px;
    background: #60a5fa;
}

.shape-2 {
    width: 400px;
    height: 400px;
    bottom: -200px;
    left: -150px;
    background: #93c5fd;
}

.shape-3 {
    width: 300px;
    height: 300px;
    top: 40%;
    left: 20%;
    background: #bfdbfe;
}

.shape-4 {
    width: 250px;
    height: 250px;
    bottom: 30%;
    right: 20%;
    background: #dbeafe;
}

@media (max-width: 1024px) {
    .login-wrapper {
        flex-direction: column;
    }

    .login-left {
        padding: 2rem;
    }

    .login-right {
        padding: 2rem;
    }

    .brand-description h2 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 1rem;
    }
    
    .login-box {
        padding: 2rem;
    }
    
    .logo h1 {
        font-size: 1.5rem;
    }
    
    .logo-icon {
        width: 48px;
        height: 48px;
        font-size: 1.5rem;
    }
    
    .login-header h2 {
        font-size: 1.5rem;
    }
    
    .input-group input {
        padding: 1rem 0;
        font-size: 1rem;
    }
    
    .input-group label {
        top: 1rem;
        font-size: 1rem;
    }
    
    .login-button {
        padding: 1rem;
        font-size: 1rem;
    }
} 